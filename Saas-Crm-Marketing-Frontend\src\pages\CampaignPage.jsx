import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, Loader2 } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast, { Toaster } from 'react-hot-toast';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

function formatDateTime(value) {
    if (!value) return '';
    const date = new Date(value);
    return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:mm
}

function CampaignForm({ form, setForm }) {
    return (
        <div className="space-y-4">
            <div>
                <Label className="mb-3" htmlFor="name">Name</Label>
                <Input
                    id="name"
                    name="name"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    className="w-full"
                />
            </div>
            <div>
                <Label className="mb-3" htmlFor="type">Type</Label>
                <Select
                    name="type"
                    value={form.type}
                    onValueChange={(value) => setForm({ ...form, type: value })}
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select campaign type" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                        <SelectItem value="push">Push Notification</SelectItem>
                    </SelectContent>
                </Select>
            </div>
            <div>
                <Label className="mb-3" htmlFor="description">Description</Label>
                <Input
                    id="description"
                    name="description"
                    value={form.description}
                    onChange={(e) => setForm({ ...form, description: e.target.value })}
                    className="w-full"
                />
            </div>
            <div>
                <Label className="mb-3" htmlFor="scheduled_at">Scheduled At</Label>
                <Input
                    id="scheduled_at"
                    name="scheduled_at"
                    type="datetime-local"
                    value={form.scheduled_at}
                    onChange={(e) => setForm({ ...form, scheduled_at: e.target.value })}
                    className="w-full"
                />
            </div>
            <div>
                <Label className="mb-3" htmlFor="status">Status</Label>
                <Select
                    name="status"
                    value={form.status}
                    onValueChange={(value) => setForm({ ...form, status: value })}
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="sent">Sent</SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
}

export default function CampaignPage() {
    const [campaigns, setCampaigns] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);

    const [currentCampaign, setCurrentCampaign] = useState(null);

    const [form, setForm] = useState({
        name: '',
        type: '',
        description: '',
        scheduled_at: '',
        status: 'draft'
    });

    useEffect(() => {
        fetchCampaigns();
    }, []);

    const fetchCampaigns = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/marketing/campaigns');
            setCampaigns(res.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleAddCampaign = async () => {
        setFormLoading(true);
        try {
            await apiClient.post('/marketing/campaigns', form);
            toast.success('Campaign added successfully!');
            setShowAdd(false);
            resetForm();
            fetchCampaigns();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleEditCampaign = async () => {
        setFormLoading(true);
        try {
            await apiClient.patch(`/marketing/campaigns/${currentCampaign.id}`, form);
            toast.success('Campaign updated successfully!');
            setShowEdit(false);
            setCurrentCampaign(null);
            resetForm();
            fetchCampaigns();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteCampaign = async (id) => {
        setIsLoading(true);
        try {
            await apiClient.delete(`/marketing/campaigns/${id}`);
            toast.success('Campaign deleted successfully!');
            fetchCampaigns();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const openAddModal = () => {
        resetForm();
        setShowAdd(true);
    };

    const openEditModal = (campaign) => {
        setCurrentCampaign(campaign);
        setForm({
            name: campaign.name,
            type: campaign.type,
            description: campaign.description,
            scheduled_at: formatDateTime(campaign.scheduled_at),
            status: campaign.status,
        });
        setShowEdit(true);
    };

    const resetForm = () => {
        setForm({
            name: '',
            type: '',
            description: '',
            scheduled_at: '',
            status: 'draft'
        });
    };

    return (
        <Card className="p-4 rounded-none">
            <Toaster position="bottom-center" />
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Campaign Management</CardTitle>
                <Button onClick={openAddModal}>Add Campaign</Button>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Scheduled At</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" /> Loading...
                                </TableCell>
                            </TableRow>
                        ) : campaigns.length > 0 ? (
                            campaigns.map((campaign, index) => (
                                <TableRow key={campaign.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{campaign.name}</TableCell>
                                    <TableCell>{campaign.type}</TableCell>
                                    <TableCell>{campaign.description}</TableCell>
                                    <TableCell>{new Date(campaign.scheduled_at).toLocaleString()}</TableCell>
                                    <TableCell>
                                        <span
                                            className={`px-2 py-1 rounded-full text-xs font-semibold ${campaign.status === 'active'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-yellow-100 text-yellow-800'
                                                }`}
                                        >
                                            {campaign.status}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-right space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditModal(campaign)}
                                            title="Edit Campaign"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    title="Delete Campaign"
                                                    variant="destructive"
                                                    size="sm"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>
                                                    This action cannot be undone. This will permanently
                                                    delete the campaign.
                                                </p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction
                                                        onClick={() => handleDeleteCampaign(campaign.id)}
                                                    >
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">
                                    No campaigns found.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Add Campaign Dialog */}
            <Dialog open={showAdd} onOpenChange={setShowAdd}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add Campaign</DialogTitle>
                    </DialogHeader>
                    <CampaignForm form={form} setForm={setForm} />
                    <DialogFooter>
                        <Button onClick={handleAddCampaign} disabled={formLoading}>
                            {formLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : null}
                            Add
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit Campaign Dialog */}
            <Dialog open={showEdit} onOpenChange={setShowEdit}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Campaign</DialogTitle>
                    </DialogHeader>
                    <CampaignForm form={form} setForm={setForm} />
                    <DialogFooter>
                        <Button onClick={handleEditCampaign} disabled={formLoading}>
                            {formLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : null}
                            Update
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
