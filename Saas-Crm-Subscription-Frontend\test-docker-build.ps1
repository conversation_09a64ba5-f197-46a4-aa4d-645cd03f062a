# Test Docker Build Script for Windows PowerShell
# This script tests the Docker build with different scenarios

Write-Host "=== Testing Docker Build ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Build without GitHub token (should use fallback)
Write-Host "Test 1: Building without GitHub token (fallback to package.docker.json)" -ForegroundColor Yellow
Write-Host "This should work by using the fallback package.json without private Git dependency"
Write-Host ""

# Build the Docker image
Write-Host "Starting Docker build..." -ForegroundColor Green
docker compose build --no-cache --progress plain 2>&1 | Tee-Object -FilePath "build-test.log"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✅ Test 1 PASSED: Docker build completed successfully without GitHub token" -ForegroundColor Green
    Write-Host ""
    
    # Test 2: Try to run the container
    Write-Host "Test 2: Testing container startup" -ForegroundColor Yellow
    docker compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test 2 PASSED: Container started successfully" -ForegroundColor Green
        Write-Host ""
        
        # Wait a moment for the container to fully start
        Write-Host "Waiting for container to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        
        # Test 3: Check if the application is responding
        Write-Host "Test 3: Testing application health" -ForegroundColor Yellow
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:4000/" -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Test 3 PASSED: Application is responding on port 4000" -ForegroundColor Green
            } else {
                Write-Host "❌ Test 3 FAILED: Application returned status code $($response.StatusCode)" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Test 3 FAILED: Application is not responding on port 4000" -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Clean up
        Write-Host ""
        Write-Host "Cleaning up test container..." -ForegroundColor Yellow
        docker compose down
    } else {
        Write-Host "❌ Test 2 FAILED: Container failed to start" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "❌ Test 1 FAILED: Docker build failed" -ForegroundColor Red
    Write-Host ""
    Write-Host "Build log saved to build-test.log" -ForegroundColor Yellow
    Write-Host "Last 20 lines of build log:" -ForegroundColor Yellow
    Get-Content "build-test.log" | Select-Object -Last 20
}

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "If all tests passed, your Docker build issue has been resolved!" -ForegroundColor Green
Write-Host "The build now properly handles the private Git dependency by:" -ForegroundColor White
Write-Host "1. Attempting to install with proper Git URL conversion (SSH -> HTTPS)" -ForegroundColor White
Write-Host "2. Falling back to package.docker.json if private repo access fails" -ForegroundColor White
Write-Host "3. Ensuring the build completes successfully in both scenarios" -ForegroundColor White
