import axios from 'axios';

const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use((config) => {
    let token = localStorage.getItem('token');
    if (token) {
        token = token.trim().replace(/[^\x00-\x7F]/g, "");

        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error) => Promise.reject(error));

apiClient.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            localStorage.clear();

            window.location.href = import.meta.env.VITE_LOGIN_URL || '/login';
        }
        return Promise.reject(error);
    }
);

export default apiClient;
