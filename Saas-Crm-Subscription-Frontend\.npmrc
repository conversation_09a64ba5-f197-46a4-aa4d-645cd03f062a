# NPM configuration for private repositories
# This file helps npm authenticate with private GitHub repositories

# Use GitHub token for private repositories
# The token will be injected by CI/CD or local environment
@saasprojecttelecard-gif:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}

# Fallback for direct git dependencies
# This ensures npm can access private repos via git+https URLs
git-tag-version=false
fund=false
audit=false
