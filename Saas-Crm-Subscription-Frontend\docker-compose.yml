version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NPM_TOKEN=${NPM_TOKEN}
        - GITHUB_TOKEN=${GITHUB_TOKEN}
    container_name: saas-crm-frontend
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


networks:
  app-network:
    driver: bridge

volumes:
  logs:
    driver: local
