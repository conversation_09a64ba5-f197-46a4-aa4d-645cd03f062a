# Multi-stage build for React app with Nginx
FROM node:20-alpine AS builder

# Install Git and SSH client for private repository access
RUN apk add --no-cache git openssh-client curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY package.docker.json ./

# Configure npm for private repositories (if tokens are provided)
ARG NPM_TOKEN
ARG GITHUB_TOKEN

# Set up npm authentication for private packages
RUN if [ -n "$NPM_TOKEN" ]; then \
        echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > /root/.npmrc; \
    fi

# Configure npm to use Git for Git dependencies
RUN npm config set git-tag-version false && \
    npm config set fund false && \
    npm config set audit false

# Configure Git to use HTTPS instead of SSH for private repos (safer for CI/CD)
RUN git config --global url."https://github.com/".insteadOf "**************:" && \
    git config --global url."https://github.com/".insteadOf "ssh://**************/"

# Configure Git to use token authentication for private repos if token is provided
RUN if [ -n "$GITHUB_TOKEN" ]; then \
        git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/" && \
        git config --global url."https://$<EMAIL>/".insteadOf "**************:" && \
        git config --global url."https://$<EMAIL>/".insteadOf "ssh://**************/"; \
    fi

# Set up SSH for private repositories (if needed)
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    ssh-keyscan github.com >> /root/.ssh/known_hosts

# Debug: Show current Git configuration
RUN echo "Git configuration:" && git config --list | grep url

# Create a pre-install script to handle Git dependencies
RUN echo '#!/bin/sh' > /usr/local/bin/pre-install.sh && \
    echo 'echo "Pre-install: Configuring Git for private repositories..."' >> /usr/local/bin/pre-install.sh && \
    echo 'git config --global url."https://github.com/".insteadOf "**************:"' >> /usr/local/bin/pre-install.sh && \
    echo 'git config --global url."https://github.com/".insteadOf "ssh://**************/"' >> /usr/local/bin/pre-install.sh && \
    echo 'if [ -n "$GITHUB_TOKEN" ]; then' >> /usr/local/bin/pre-install.sh && \
    echo '  echo "Using GitHub token for authentication..."' >> /usr/local/bin/pre-install.sh && \
    echo '  git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/"' >> /usr/local/bin/pre-install.sh && \
    echo '  git config --global url."https://$<EMAIL>/".insteadOf "**************:"' >> /usr/local/bin/pre-install.sh && \
    echo '  git config --global url."https://$<EMAIL>/".insteadOf "ssh://**************/"' >> /usr/local/bin/pre-install.sh && \
    echo 'else' >> /usr/local/bin/pre-install.sh && \
    echo '  echo "No GitHub token provided, using public access..."' >> /usr/local/bin/pre-install.sh && \
    echo 'fi' >> /usr/local/bin/pre-install.sh && \
    echo 'echo "Git configuration updated:"' >> /usr/local/bin/pre-install.sh && \
    echo 'git config --list | grep url' >> /usr/local/bin/pre-install.sh && \
    chmod +x /usr/local/bin/pre-install.sh

# Install all dependencies (including dev dependencies needed for build)
# Add retry logic and better error handling
RUN echo "Starting dependency installation..." && \
    /usr/local/bin/pre-install.sh && \
    echo "Attempting to install with private Git dependency..." && \
    if npm install --no-audit --no-fund --verbose 2>&1; then \
        echo "Dependencies installed successfully with private repository"; \
    else \
        echo "Private repository failed, trying fallback approach..." && \
        echo "Backing up original package.json..." && \
        cp package.json package.json.backup && \
        echo "Using package.docker.json without private Git dependency..." && \
        cp package.docker.json package.json && \
        echo "Installing dependencies without private Git dependency..." && \
        npm install --no-audit --no-fund --verbose && \
        echo "Dependencies installed successfully without private Git dependency"; \
    fi && \
    echo "Verifying Vite installation..." && \
    if command -v vite >/dev/null 2>&1 || [ -f node_modules/.bin/vite ]; then \
        echo "Vite is available"; \
    else \
        echo "Vite not found, checking node_modules..." && \
        ls -la node_modules/.bin/ | grep vite || echo "Vite binary not found in node_modules/.bin/"; \
        echo "Checking if vite package is installed..." && \
        npm list vite || echo "Vite package not found"; \
    fi

# Copy source code
COPY . .

# Build the app
RUN echo "Building the application..." && \
    npx vite build || (echo "Build failed with npx, trying npm run build..." && npm run build)

# Note: Dev dependencies are not needed in production stage since we're using multi-stage build
# The production stage only copies the built dist folder, so no need to prune here

# Production stage with Nginx
FROM nginx:alpine

# Copy custom nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built app from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Ensure proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:4000/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]