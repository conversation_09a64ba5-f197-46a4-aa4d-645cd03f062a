import axios from 'axios';

const apiClient = axios.create({
<<<<<<< HEAD

    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://auth.tclaccord.com/api',
=======
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
>>>>>>> 23856c1e0c702b1c16c9b6f7b77c16b0dc90d8bc
    // baseURL: 'http://*************:3000/api',
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export default apiClient;