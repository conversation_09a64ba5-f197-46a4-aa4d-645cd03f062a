import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';
import apiClient from '@/lib/apiClient';


export default function ProductPage() {
    const [products, setProducts] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentProduct, setCurrentProduct] = useState(null);
    const [categories, setCategories] = useState([]);


    useEffect(() => {
        fetchProducts();
        fetchProductCategories();
    }, []);

    const fetchProducts = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/products');
            setProducts(res.data);
        } catch (err) {
            toast.error("Failed to fetch products.");
        } finally {
            setIsLoading(false);
        }
    };


    const fetchProductCategories = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/product-categories');
            setCategories(res.data);
        } catch (err) {
            toast.error("Failed to fetch products.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleFormSubmit = async (formData) => {
        if (!formData.name || !formData.sku || !formData.price || !formData.stock_quantity) {
            toast.error("Required fields are missing.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            if (isUpdate) {
                const { data: updatedProduct } = await apiClient.patch(
                    `/products/${formData.id}`,
                    formData
                );

                fetchProducts()
                toast.success("Product updated successfully!");
            } else {
                const { data: newProduct } = await apiClient.post("/products", formData);
                // setProducts((prev) => [newProduct, ...prev]);
                fetchProducts()
                toast.success("Product created successfully!");
            }
            setIsModalOpen(false);
            setCurrentProduct(null);
        } catch (err) {
            toast.error(`Failed to ${isUpdate ? "update" : "create"} product.`);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteProduct = async (id) => {
        const confirmed = window.confirm("Are you sure you want to delete this product?");
        if (!confirmed) {
            return;
        }

        try {
            await apiClient.delete(`/products/${id}`);
            setProducts((prev) => prev.filter((p) => p.id !== id));
            toast.success("Product deleted successfully!");
        } catch (err) {
            toast.error("Failed to delete product.");
        }
    };

    const handleAddProduct = () => {
        setCurrentProduct(null);
        setIsModalOpen(true);
    };

    const handleEditProduct = (product) => {
        setCurrentProduct(product);
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Products</CardTitle>
                <Button onClick={handleAddProduct}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Product
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Product Name</TableHead>
                            <TableHead>SKU</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Stock</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="6" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : products.length > 0 ? (
                            products.map(product => (
                                <TableRow key={product.id}>
                                    <TableCell className="font-medium">{product.name}</TableCell>
                                    <TableCell>{product.sku}</TableCell>
                                    <TableCell>${product.price}</TableCell>
                                    <TableCell>{product.stock_quantity}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {product.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleEditProduct(product)}
                                        >
                                            <Pencil className="h-4 w-4" /> Edit
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="destructive"
                                            onClick={() => handleDeleteProduct(product.id)}
                                        >
                                            <Trash2 className="h-4 w-4" /> Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="6" className="text-center">No products found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Product Modal for Add/Edit */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentProduct ? "Edit Product" : "Create Product"}</DialogTitle>
                    </DialogHeader>
                    <ProductForm
                        initialData={currentProduct}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                        categories={categories}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function ProductForm({ initialData, onSubmit, isLoading, categories }) {
    const [formData, setFormData] = useState(initialData || {
        name: '',
        sku: '',
        price: 0,
        description: '',
        categoryId: '',
        stock_quantity: 0,
        is_active: true
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value, type, checked } = e.target;
        // setFormData(prev => ({
        //     ...prev,
        //     [id]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
        // }));
        setFormData(prev => ({
            ...prev,
            [id]:
                type === 'checkbox'
                    ? checked
                    : type === 'number'
                        ? (id === 'price' ? parseInt(value, 10) : Number(value))
                        : value
        }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input id="name" type="text" value={formData.name} onChange={handleInputChange} placeholder="e.g. Laptop Pro" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input id="sku" type="text" value={formData.sku} onChange={handleInputChange} placeholder="e.g. LAP-P-101" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="price">Price</Label>
                <Input id="price" type="number" value={formData.price} onChange={handleInputChange} placeholder="e.g. 1200.00" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input id="description" type="text" value={formData.description} onChange={handleInputChange} placeholder="e.g. A high-performance laptop for professionals." />
            </div>
            <div className="space-y-2">
                <Label htmlFor="categoryId">Category ID</Label>
                <select
                    id="categoryId"
                    value={formData.categoryId}
                    onChange={handleInputChange}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                    <option value="">Select a Category</option>
                    {categories.map((cat) => (
                        <option key={cat.id} value={cat.id}>
                            {cat.name}
                        </option>
                    ))}
                </select>
                {/* <Input id="categoryId" type="text" value={formData.categoryId} onChange={handleInputChange} placeholder="e.g. 1" /> */}
            </div>
            <div className="space-y-2">
                <Label htmlFor="stock_quantity">Stock Quantity</Label>
                <Input id="stock_quantity" type="number" value={formData.stock_quantity} onChange={handleInputChange} placeholder="e.g. 50" />
            </div>
            <div className="flex items-center space-x-2">
                <Checkbox id="is_active" checked={formData.is_active} onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))} />
                <Label htmlFor="is_active">Is Active</Label>
            </div>
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Product" : "Create Product"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
