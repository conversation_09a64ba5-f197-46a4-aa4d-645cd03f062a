import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, PlusCircle, Loader2 } from 'lucide-react';
import { apiClientRole as apiClient } from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function PermissionPage() {
    const [permissions, setPermissions] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentPermission, setCurrentPermission] = useState(null);

    const [form, setForm] = useState({ name: '', description: '' });

    useEffect(() => {
        fetchPermissions();
    }, []);

    const fetchPermissions = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/permissions');
            setPermissions(res.data);
        } catch (err) {
            toast.error("Failed to fetch permissions.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleFormSubmit = async () => {
        setFormLoading(true);
        const isUpdate = !!currentPermission;

        try {
            if (isUpdate) {
                await apiClient.patch(`/permissions/${currentPermission.id}`, form);
                toast.success("Permission updated successfully!");
            } else {
                await apiClient.post("/permissions", form);
                toast.success("Permission created successfully!");
            }
            setIsModalOpen(false);
            setCurrentPermission(null);
            setForm({ name: '', description: '' });
            fetchPermissions();
        } catch (err) {
            toast.error(`Failed to ${isUpdate ? "update" : "create"} permission.`);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeletePermission = async (id) => {
        setFormLoading(true);
        try {
            await apiClient.delete(`/permissions/${id}`);
            toast.success("Permission deleted successfully!");
            fetchPermissions();
        } catch (err) {
            toast.error("Failed to delete permission.");
        } finally {
            setFormLoading(false);
        }
    };

    const handleAddClick = () => {
        setCurrentPermission(null);
        setForm({ name: '', description: '' });
        setIsModalOpen(true);
    };

    const handleEditClick = (permission) => {
        setCurrentPermission(permission);
        setForm({ name: permission.name, description: permission.description });
        setIsModalOpen(true);
    };

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm(prev => ({ ...prev, [id]: value }));
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Permission Management</CardTitle>
                <Button onClick={handleAddClick}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Permission
                </Button>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Permission Name</TableHead>
                            <TableHead>Description</TableHead>
                            {/* <TableHead className="text-right">Actions</TableHead> */}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" />
                                    Loading permissions...
                                </TableCell>
                            </TableRow>
                        ) : permissions.length > 0 ? (
                            permissions.map((permission, index) => (
                                <TableRow key={permission.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{permission.name}</TableCell>
                                    <TableCell>{permission.description}</TableCell>
                                    {/* <TableCell className="text-right space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleEditClick(permission)}
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="sm" disabled={formLoading}>
                                                    {formLoading ? (
                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <Trash2 className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone. This will permanently delete the permission.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeletePermission(permission.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell> */}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">No permissions found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{currentPermission ? 'Edit Permission' : 'Add Permission'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="name" className="mb-3">Name</Label>
                            <Input
                                id="name"
                                value={form.name}
                                onChange={handleInputChange}
                            />
                        </div>
                        <div>
                            <Label htmlFor="description" className="mb-3">Description</Label>
                            <Input
                                id="description"
                                value={form.description}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleFormSubmit} disabled={formLoading}>
                            {formLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                currentPermission ? 'Update' : 'Add'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}