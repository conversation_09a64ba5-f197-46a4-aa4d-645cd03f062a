import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function AddUserPage() {
    const navigate = useNavigate();
    const [form, setForm] = useState({ name: '', email: '' });

    const handleSubmit = () => {
        console.log('New User:', form);
        // Call backend API here
        navigate('/');
    };

    return (
        <Card className="p-6 max-w-md mx-auto mt-10">
            <CardHeader>
                <CardTitle>Add User</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div>
                    <Label>Name</Label>
                    <Input value={form.name} onChange={e => setForm({ ...form, name: e.target.value })} />
                </div>
                <div>
                    <Label>Email</Label>
                    <Input value={form.email} onChange={e => setForm({ ...form, email: e.target.value })} />
                </div>
                <Button className="mt-4 w-full" onClick={handleSubmit}>
                    Save
                </Button>
            </CardContent>
        </Card>
    );
}
