version: "3.8"

services:
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
    env_file:
      - .env
    networks:
      - app-network
    profiles:
      - dev

  # Production service
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NPM_TOKEN: ${NPM_TOKEN}
        GITHUB_TOKEN: ${GITHUB_TOKEN}
        VITE_LOGIN_URL: ${VITE_LOGIN_URL}
        VITE_API_BASE_URL: ${VITE_API_BASE_URL}
        VITE_API_BASE_URL_AUTH: ${VITE_API_BASE_URL_AUTH}
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  node_modules:
