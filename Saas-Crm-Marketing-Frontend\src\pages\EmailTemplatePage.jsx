import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, Send, Loader2 } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import toast, { Toaster } from 'react-hot-toast';

const mockDb = [
    { id: 1, name: 'Welcome Email', subject: 'Welcome, {{name}}!', body: 'Hello {{name}}, welcome to our service!' },
    { id: 2, name: 'Password Reset', subject: 'Reset Your Password', body: 'Please click the link to reset your password.' },
];

let nextId = 3;
import apiClient from '@/lib/apiClient';

export default function EmailTemplatePage() {
    const [templates, setTemplates] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [showSend, setShowSend] = useState(false);

    const [currentTemplate, setCurrentTemplate] = useState(null);

    const [form, setForm] = useState({
        name: '',
        subject: '',
        body: ''
    });

    const [sendEmailForm, setSendEmailForm] = useState({
        to: '',
        variables: '{}'
    });

    useEffect(() => {
        fetchTemplates();
    }, []);



    const fetchTemplates = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/email-templates');
            setTemplates(res.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setForm({ ...form, [name]: value });
    };

    const handleSendEmailFormChange = (e) => {
        const { name, value } = e.target;
        setSendEmailForm({ ...sendEmailForm, [name]: value });
    };

    const handleAddTemplate = async () => {
        setFormLoading(true);
        try {
            await apiClient.post('/email-templates', form);
            toast.success('Template added successfully!');
            setShowAdd(false);
            setForm({ name: '', subject: '', body: '' });
            fetchTemplates();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleEditTemplate = async () => {
        setFormLoading(true);
        try {
            await apiClient.patch(`/email-templates/${currentTemplate.id}`, form);
            toast.success('Template updated successfully!');
            setShowEdit(false);
            setCurrentTemplate(null);
            setForm({ name: '', subject: '', body: '' });
            fetchTemplates();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteTemplate = async (id) => {
        setIsLoading(true);
        try {
            await apiClient.delete(`/email-templates/${id}`);
            toast.success('Template deleted successfully!');
            fetchTemplates();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleSendEmail = async () => {
        setFormLoading(true);
        try {
            const parsedVariables = JSON.parse(sendEmailForm.variables);
            const payload = {
                to: sendEmailForm.to,
                variables: parsedVariables
            };
            await apiClient.post(`/email-templates/${currentTemplate.id}/send`, payload);
            toast.success('Email sent successfully!');
            setShowSend(false);
            setSendEmailForm({ to: '', variables: '{}' });
        } catch (err) {
            if (err instanceof SyntaxError) {
                toast.error('Invalid JSON in variables field.');
            } else {
                toast.error(err.response?.data?.message || err.message);
            }
        } finally {
            setFormLoading(false);
        }
    };

    const openAddModal = () => {
        setForm({ name: '', subject: '', body: '' });
        setShowAdd(true);
    };

    const openEditModal = (template) => {
        setCurrentTemplate(template);
        setForm({
            name: template.name,
            subject: template.subject,
            body: template.body,
        });
        setShowEdit(true);
    };

    const openSendModal = (template) => {
        setCurrentTemplate(template);
        setSendEmailForm({ to: '', variables: '{}' });
        setShowSend(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <Toaster position="bottom-center" />
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Email Template Management</CardTitle>
                <Button onClick={openAddModal}>Add Template</Button>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Subject</TableHead>
                            <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" /> Loading...
                                </TableCell>
                            </TableRow>
                        ) : templates.length > 0 ? (
                            templates.map((template, index) => (
                                <TableRow key={template.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{template.name}</TableCell>
                                    <TableCell>{template.subject}</TableCell>
                                    <TableCell align="right" className="space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditModal(template)}
                                            title="Edit Template"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button title="Delete Template" variant="destructive" size="sm">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone. This will permanently delete the template.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteTemplate(template.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                        <Button
                                            variant="default"
                                            size="sm"
                                            onClick={() => openSendModal(template)}
                                            title="Send Email"
                                        >
                                            <Send className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">No templates found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Add Template Dialog */}
            <Dialog open={showAdd} onOpenChange={setShowAdd}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add Template</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className="mb-3" htmlFor="name">Name</Label>
                            <Input id="name" name="name" value={form.name} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3" htmlFor="subject">Subject</Label>
                            <Input id="subject" name="subject" value={form.subject} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3" htmlFor="body">Body</Label>
                            <Textarea id="body" name="body" value={form.body} onChange={handleChange} />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleAddTemplate} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Add
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit Template Dialog */}
            <Dialog open={showEdit} onOpenChange={setShowEdit}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Template</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className="mb-3" htmlFor="name">Name</Label>
                            <Input id="name" name="name" value={form.name} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3" htmlFor="subject">Subject</Label>
                            <Input id="subject" name="subject" value={form.subject} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3" htmlFor="body">Body</Label>
                            <Textarea id="body" name="body" value={form.body} onChange={handleChange} />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleEditTemplate} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Update
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Send Email Dialog */}
            <Dialog open={showSend} onOpenChange={setShowSend}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Send "{currentTemplate?.name}"</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className="mb-3" htmlFor="to">Recipient Email</Label>
                            <Input
                                id="to"
                                name="to"
                                type="email"
                                value={sendEmailForm.to}
                                onChange={handleSendEmailFormChange}
                            />
                        </div>
                        <div>
                            <Label className="mb-3" htmlFor="variables">Variables (JSON)</Label>
                            <Textarea
                                id="variables"
                                name="variables"
                                value={sendEmailForm.variables}
                                onChange={handleSendEmailFormChange}
                                className="min-h-[100px]"
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleSendEmail} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Send Email
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
