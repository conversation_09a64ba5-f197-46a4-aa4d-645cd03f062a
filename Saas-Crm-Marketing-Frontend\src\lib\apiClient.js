import axios from 'axios';

const apiClient = axios.create({
    // baseURL: 'http://192.168.88.71:3003/api',
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

apiClient.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            localStorage.clear();

            window.location.href = import.meta.env.VITE_LOGIN_URL;
        }
        return Promise.reject(error);
    }
);



const apiClientRole = axios.create({
    // baseURL: 'http://192.168.88.71:3003/api',
    baseURL: import.meta.env.VITE_API_BASE_URL_ROLE,
    headers: {
        'Content-Type': 'application/json',
    },
});

apiClientRole.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

apiClientRole.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 401) {
            localStorage.clear();

            window.location.href = import.meta.env.VITE_LOGIN_URL;
        }
        return Promise.reject(error);
    }
);

export { apiClientRole };
export default apiClient;