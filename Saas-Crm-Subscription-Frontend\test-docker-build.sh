#!/bin/bash

# Test Docker Build Script
# This script tests the Docker build with different scenarios

echo "=== Testing Docker Build ==="
echo ""

# Test 1: Build without GitHub token (should use fallback)
echo "Test 1: Building without GitHub token (fallback to package.docker.json)"
echo "This should work by using the fallback package.json without private Git dependency"
echo ""

docker compose build --no-cache --progress plain 2>&1 | tee build-test.log

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Test 1 PASSED: Docker build completed successfully without GitHub token"
    echo ""
    
    # Test 2: Try to run the container
    echo "Test 2: Testing container startup"
    docker compose up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Test 2 PASSED: Container started successfully"
        echo ""
        
        # Wait a moment for the container to fully start
        sleep 5
        
        # Test 3: Check if the application is responding
        echo "Test 3: Testing application health"
        if curl -f http://localhost:4000/ > /dev/null 2>&1; then
            echo "✅ Test 3 PASSED: Application is responding on port 4000"
        else
            echo "❌ Test 3 FAILED: Application is not responding on port 4000"
        fi
        
        # Clean up
        echo ""
        echo "Cleaning up test container..."
        docker compose down
    else
        echo "❌ Test 2 FAILED: Container failed to start"
    fi
else
    echo ""
    echo "❌ Test 1 FAILED: Docker build failed"
    echo ""
    echo "Build log saved to build-test.log"
    echo "Last 20 lines of build log:"
    tail -20 build-test.log
fi

echo ""
echo "=== Test Summary ==="
echo "If all tests passed, your Docker build issue has been resolved!"
echo "The build now properly handles the private Git dependency by:"
echo "1. Attempting to install with proper Git URL conversion (SSH -> HTTPS)"
echo "2. Falling back to package.docker.json if private repo access fails"
echo "3. Ensuring the build completes successfully in both scenarios"
