import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import apiClient from '../lib/apiClient';
import toast from 'react-hot-toast';

// Main component for the Categories Page
export default function CategoriesPage() {
    const [categories, setCategories] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentCategory, setCurrentCategory] = useState(null);

    // Fetch categories on component mount
    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/product-categories');
            setCategories(res.data);
        } catch (err) {
            toast.error("Failed to fetch categories.");
        } finally {
            setIsLoading(false);
        }
    };

    // Handle form submission for creating or updating a category
    const handleFormSubmit = async (formData) => {
        if (!formData.name) {
            toast.error("Category name is required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            if (isUpdate) {
                const { data: updatedCategory } = await apiClient.patch(
                    `/product-categories/${formData.id}`,
                    { name: formData.name, description: formData.description }
                );

                fetchCategories();
                toast.success("Category updated successfully!");
            } else {
                const { data: newCategory } = await apiClient.post("/product-categories", { name: formData.name, description: formData.description });
                // setCategories((prev) => [newCategory, ...prev]);
                fetchCategories()
                toast.success("Category created successfully!");
            }
            setIsModalOpen(false);
            setCurrentCategory(null);
        } catch (err) {
            toast.error(`Failed to ${isUpdate ? "update" : "create"} category.`);
        } finally {
            setFormLoading(false);
        }
    };

    // Handle category deletion
    const handleDeleteCategory = async (id) => {
        const confirmed = window.confirm("Are you sure you want to delete this category?");
        if (!confirmed) {
            return;
        }

        try {
            await apiClient.delete(`/product-categories/${id}`);
            // setCategories((prev) => prev.filter((c) => c.id !== id));
            fetchCategories();
            toast.success("Category deleted successfully!");
        } catch (err) {
            toast.error("Failed to delete category.");
        }
    };

    // Handlers to open modals
    const handleAddCategory = () => {
        setCurrentCategory(null);
        setIsModalOpen(true);
    };

    const handleEditCategory = (category) => {
        setCurrentCategory(category);
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Category Management</CardTitle>
                <Button onClick={handleAddCategory}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Category
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Category Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : categories.length > 0 ? (
                            categories.map(category => (
                                <TableRow key={category.id}>
                                    <TableCell className="font-medium">{category.name}</TableCell>
                                    <TableCell>{category.description}</TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleEditCategory(category)}
                                        >
                                            <Pencil className="h-4 w-4" /> Edit
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="destructive"
                                            onClick={() => handleDeleteCategory(category.id)}
                                        >
                                            <Trash2 className="h-4 w-4" /> Delete
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">No categories found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Category Modal for Add/Edit */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentCategory ? "Edit Category" : "Create Category"}</DialogTitle>
                    </DialogHeader>
                    <CategoryForm
                        initialData={currentCategory}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function CategoryForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || { name: '', description: '' });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="name">Category Name</Label>
                <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g. Electronics"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="description">Category Description</Label>
                <Input
                    id="description"
                    type="text"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="e.g. Products like computers and phones"
                />
            </div>
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Category" : "Create Category"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
