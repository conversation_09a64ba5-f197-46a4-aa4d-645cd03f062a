import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2, PlusCircle } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function LeadPage() {
    const [leads, setLeads] = useState([]);
    const [contacts, setContacts] = useState([]);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [currentLead, setCurrentLead] = useState(null);
    const [form, setForm] = useState({
        name: '',
        contact_id: '',
        source: '',
        status: 'new',
        score: '',
        notes: ''
    });
    const [leadToDeleteId, setLeadToDeleteId] = useState(null);

    // --- API Calls ---
    const fetchLeads = () => {
        apiClient.get('/leads')
            .then(res => setLeads(res.data))
            .catch(err => toast.error(err.message));
    };

    const fetchContacts = () => {
        apiClient.get('/contacts')
            .then(res => setContacts(res.data))
            .catch(err => toast.error(err.message));
    };

    useEffect(() => {
        fetchLeads();
        fetchContacts();
    }, []);

    // --- Handlers ---
    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm(prev => ({ ...prev, [id]: value }));
    };

    const openAddModal = () => {
        setForm({
            name: '',
            contact_id: '',
            source: '',
            status: 'new',
            score: '',
            notes: ''
        });
        setShowAdd(true);
    };

    const openEditModal = (lead) => {
        setCurrentLead(lead);
        setForm({
            name: lead.name || '',
            contact_id: lead.contact_id || '',
            source: lead.source || '',
            status: lead.status || 'new',
            score: lead.score || '',
            notes: lead.notes || ''
        });
        setShowEdit(true);
    };

    // --- CRUD ---
    const validateForm = () => {
        if (!form.name || !form.contact_id) {
            toast.error('Name and Contact are required.');
            return false;
        }
        return true;
    };

    const handleAddLead = async () => {
        if (!validateForm()) return;

        const payload = {
            ...form,
            owner_id: "7d00ad75-61c6-4f95-b574-dcff9ca01586"
        };

        try {
            await apiClient.post('/leads', payload);
            toast.success('Lead created successfully');
            fetchLeads();
            setShowAdd(false);
        } catch (err) {
            toast.error(err.message);
        }
    };

    const handleUpdateLead = async () => {
        if (!validateForm()) return;

        const payload = {
            ...form,
            owner_id: currentLead?.owner_id || localStorage.getItem('token')
        };

        try {
            await apiClient.patch(`/leads/${currentLead.id}`, payload);
            toast.success('Lead updated successfully');
            fetchLeads();
            setShowEdit(false);
            setCurrentLead(null);
        } catch (err) {
            toast.error(err.message);
        }
    };

    const handleDeleteLeadConfirmed = async () => {
        try {
            await apiClient.delete(`/leads/${leadToDeleteId}`);
            toast.success('Lead deleted successfully');
            fetchLeads();
            setLeadToDeleteId(null);
        } catch (err) {
            toast.error(err.message);
        }
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Lead Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button onClick={openAddModal}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Add Lead
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>Add New Lead</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div>
                                <Label htmlFor="name" className="mb-4">Name</Label>
                                <Input id="name" value={form.name} onChange={handleInputChange} />
                            </div>
                            <div>
                                <Label htmlFor="contact_id" className="mb-4">Contact</Label>
                                <select
                                    id="contact_id"
                                    value={form.contact_id}
                                    onChange={handleInputChange}
                                    className="w-full border rounded p-2"
                                >
                                    <option value="">Select Contact</option>
                                    {contacts.map((c) => (
                                        <option key={c.id} value={c.id}>
                                            {c.first_name} {c.last_name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <Label htmlFor="source" className="mb-4">Source</Label>
                                <select
                                    id="source"
                                    value={form.source}
                                    onChange={handleInputChange}
                                    className="w-full border rounded p-2"
                                >
                                    <option value="">Select Source</option>
                                    <option value="website">Website</option>
                                    <option value="referral">Referral</option>
                                    <option value="social_media">Social Media</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div>
                                <Label htmlFor="status" className="mb-4">Status</Label>
                                <select
                                    id="status"
                                    value={form.status}
                                    onChange={handleInputChange}
                                    className="w-full border rounded p-2"
                                >
                                    <option value="new">New</option>
                                    <option value="existing">Existing</option>
                                    <option value="converted">Converted</option>
                                </select>
                            </div>
                            <div>
                                <Label htmlFor="score" className="mb-4">Score</Label>
                                <Input id="score" type="number" value={form.score} onChange={handleInputChange} />
                            </div>
                            <div>
                                <Label htmlFor="notes" className="mb-4">Notes</Label>
                                <Textarea id="notes" value={form.notes} onChange={handleInputChange} />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button onClick={handleAddLead}>Save</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                {leads.length === 0 ? (
                    <div className="text-center text-gray-500 py-10">No leads found.</div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>#</TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Source</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Score</TableHead>
                                <TableHead>Notes</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {leads.map((lead, index) => (
                                <TableRow key={lead.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{lead.name}</TableCell>
                                    <TableCell>{lead.source}</TableCell>
                                    <TableCell>{lead.status}</TableCell>
                                    <TableCell>{lead.score}</TableCell>
                                    <TableCell>{lead.notes}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        {/* Edit */}
                                        <Dialog open={showEdit && currentLead?.id === lead.id} onOpenChange={setShowEdit}>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => openEditModal(lead)}
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            </DialogTrigger>
                                            {currentLead && (
                                                <DialogContent className="max-h-[80vh] overflow-y-auto">
                                                    <DialogHeader>
                                                        <DialogTitle>Edit Lead</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="space-y-4 py-4">
                                                        <div>
                                                            <Label htmlFor="name" className="mb-4">Name</Label>
                                                            <Input id="name" value={form.name} onChange={handleInputChange} />
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="contact_id" className="mb-4">Contact</Label>
                                                            <select
                                                                id="contact_id"
                                                                value={form.contact_id}
                                                                onChange={handleInputChange}
                                                                className="w-full border rounded p-2"
                                                            >
                                                                <option value="">Select Contact</option>
                                                                {contacts.map((c) => (
                                                                    <option key={c.id} value={c.id}>
                                                                        {c.first_name} {c.last_name}
                                                                    </option>
                                                                ))}
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="source" className="mb-4">Source</Label>
                                                            <select
                                                                id="source"
                                                                value={form.source}
                                                                onChange={handleInputChange}
                                                                className="w-full border rounded p-2"
                                                            >
                                                                <option value="">Select Source</option>
                                                                <option value="website">Website</option>
                                                                <option value="referral">Referral</option>
                                                                <option value="social_media">Social Media</option>
                                                                <option value="other">Other</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="status" className="mb-4">Status</Label>
                                                            <select
                                                                id="status"
                                                                value={form.status}
                                                                onChange={handleInputChange}
                                                                className="w-full border rounded p-2"
                                                            >
                                                                <option value="new">New</option>
                                                                <option value="existing">Existing</option>
                                                                <option value="converted">Converted</option>
                                                            </select>
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="score" className="mb-4">Score</Label>
                                                            <Input id="score" type="number" value={form.score} onChange={handleInputChange} />
                                                        </div>
                                                        <div>
                                                            <Label htmlFor="notes" className="mb-4">Notes</Label>
                                                            <Textarea id="notes" value={form.notes} onChange={handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <DialogFooter>
                                                        <Button onClick={handleUpdateLead}>Save Changes</Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            )}
                                        </Dialog>

                                        {/* Delete */}
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => setLeadToDeleteId(lead.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This will permanently delete the lead.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handleDeleteLeadConfirmed}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
    );
}
