import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, Loader2, DollarSign } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast, { Toaster } from 'react-hot-toast';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

const HARDCODED_OWNER_ID = "7d00ad75-61c6-4f95-b574-dcff9ca01586";

const OPPORTUNITY_STAGES = [
    'prospecting',
    'qualification',
    'proposal',
    'negotiation',
    'closed_won',
    'closed_lost'
];
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0
    }).format(amount);
};

export default function OpportunityPage() {
    const [opportunities, setOpportunities] = useState([]);
    const [leads, setLeads] = useState([]);
    const [contacts, setContacts] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);

    const [currentOpportunity, setCurrentOpportunity] = useState(null);

    const initialFormState = {
        title: '',
        amount: '',
        stage: OPPORTUNITY_STAGES[0],
        lead_id: 'null',
        contact_id: 'null',
        expected_close_date: new Date().toISOString().split('T')[0],
        notes: ''
    };
    const [form, setForm] = useState(initialFormState);

    useEffect(() => {
        const fetchAllData = async () => {
            setIsLoading(true);
            try {
                const [opportunitiesRes, leadsRes, contactsRes] = await Promise.all([
                    apiClient.get('/opportunities'),
                    apiClient.get('/leads'),
                    apiClient.get('/contacts')
                ]);
                setOpportunities(opportunitiesRes.data);
                setLeads(leadsRes.data);
                setContacts(contactsRes.data);
            } catch (err) {
                toast.error("Failed to load data: " + (err.response?.data?.message || err.message));
            } finally {
                setIsLoading(false);
            }
        };

        fetchAllData();
    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setForm({ ...form, [name]: value });
    };

    // const handleSelectChange = (name, value) => {
    //     if (name === 'lead_id') {
    //         setForm({ ...form, lead_id: value, contact_id: 'null' });
    //     } else if (name === 'contact_id') {
    //         setForm({ ...form, contact_id: value, lead_id: 'null' });
    //     } else {
    //         setForm({ ...form, [name]: value });
    //     }
    // };

    const handleSelectChange = (name, value) => {
        // if (name === 'lead_id') {
        //     setForm({
        //         ...form,
        //         lead_id: value,
        //         contact_id: value === 'null' ? form.contact_id : 'null', // only clear if a lead is chosen
        //     });
        // } else if (name === 'contact_id') {
        //     setForm({
        //         ...form,
        //         contact_id: value,
        //         lead_id: value === 'null' ? form.lead_id : 'null', 
        //     });
        // } else {
        // }
        setForm({ ...form, [name]: value });
    };


    const handleAddOpportunity = async () => {
        setFormLoading(true);
        try {
            const payload = {
                ...form,
                amount: form.amount ? parseFloat(form.amount) : 0,
                // owner_id: HARDCODED_OWNER_ID,
                lead_id: form.lead_id === 'null' ? null : form.lead_id,
                contact_id: form.contact_id === 'null' ? null : form.contact_id,
            };
            await apiClient.post('/opportunities', payload);
            toast.success('Opportunity added successfully!');
            setShowAdd(false);
            setForm(initialFormState);
            const [opportunitiesRes] = await Promise.all([apiClient.get('/opportunities')]);
            setOpportunities(opportunitiesRes.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleEditOpportunity = async () => {
        setFormLoading(true);
        try {
            const payload = {
                ...form,
                amount: form.amount ? parseFloat(form.amount) : 0,
                // owner_id: HARDCODED_OWNER_ID,
                lead_id: form.lead_id === 'null' ? null : form.lead_id,
                contact_id: form.contact_id === 'null' ? null : form.contact_id,
            };
            await apiClient.patch(`/opportunities/${currentOpportunity.id}`, payload);
            toast.success('Opportunity updated successfully!');
            setShowEdit(false);
            setCurrentOpportunity(null);
            setForm(initialFormState);
            const [opportunitiesRes] = await Promise.all([apiClient.get('/opportunities')]);
            setOpportunities(opportunitiesRes.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteOpportunity = async (id) => {
        setIsLoading(true);
        try {
            await apiClient.delete(`/opportunities/${id}`);
            toast.success('Opportunity deleted successfully!');
            setOpportunities(opportunities.filter(opp => opp.id !== id));
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const openAddModal = () => {
        setForm(initialFormState);
        setShowAdd(true);
    };

    const openEditModal = (opportunity) => {
        setCurrentOpportunity(opportunity);
        setForm({
            title: opportunity.title,
            amount: opportunity.amount || 0,
            stage: opportunity.stage,
            lead_id: opportunity.lead_id || 'null',
            contact_id: opportunity.contact_id || 'null',
            expected_close_date: new Date(opportunity.expected_close_date).toISOString().split('T')[0],
            notes: opportunity.notes || ''
        });
        setShowEdit(true);
    };

    const getLeadName = (leadId) => leads.find(l => l.id === leadId)?.name || 'N/A';
    const getContactName = (contactId) => contacts.find(c => c.id === contactId)?.first_name || 'N/A';

    const getStageColor = (stage) => {
        switch (stage) {
            case 'prospecting':
                return 'bg-indigo-100 text-indigo-800';
            case 'qualification':
                return 'bg-purple-100 text-purple-800';
            case 'proposal':
                return 'bg-yellow-100 text-yellow-800';
            case 'negotiation':
                return 'bg-blue-100 text-blue-800';
            case 'closed_won':
                return 'bg-green-100 text-green-800';
            case 'closed_lost':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };


    return (
        <Card className="p-4 rounded-none">
            <Toaster position="bottom-center" />
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Opportunity Management</CardTitle>
                <Button onClick={openAddModal}>
                    Add Opportunity
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Stage</TableHead>
                            <TableHead>Contact</TableHead>
                            <TableHead>Lead</TableHead>
                            <TableHead>Close Date</TableHead>

                            <TableHead>Notes</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" /> Loading...
                                </TableCell>
                            </TableRow>
                        ) : opportunities.length > 0 ? (
                            opportunities.map((opportunity, index) => (
                                <TableRow key={opportunity.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{opportunity.title}</TableCell>
                                    <TableCell>{opportunity.amount}</TableCell>
                                    {/* <TableCell>{formatCurrency(opportunity.amount)}</TableCell> */}
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${getStageColor(opportunity.stage)}`}>
                                            {opportunity.stage.replace('-', ' ')}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        {getContactName(opportunity.contact_id)}
                                    </TableCell>
                                    <TableCell>
                                        {getLeadName(opportunity.lead_id)}
                                    </TableCell>
                                    <TableCell>{new Date(opportunity.expected_close_date).toLocaleDateString()}</TableCell>
                                    <TableCell>
                                        {opportunity.notes}
                                    </TableCell>
                                    <TableCell className="text-right space-x-2">
                                        <Button variant="outline" size="sm" onClick={() => openEditModal(opportunity)} title="Edit Opportunity">
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button title="Delete Opportunity" variant="destructive" size="sm">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action will permanently delete the opportunity: **{opportunity.title}**</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteOpportunity(opportunity.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">No opportunities found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            <Dialog open={showAdd || showEdit} onOpenChange={showAdd ? setShowAdd : setShowEdit}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{showAdd ? 'Add New Opportunity' : 'Edit Opportunity'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="title" className="mb-3">Title</Label>
                            <Input id="title" name="title" value={form.title} onChange={handleChange} required />
                        </div>
                        <div>
                            <Label htmlFor="amount" className="mb-3">Amount</Label>
                            <Input id="amount" name="amount" type="number" value={form.amount} onChange={handleChange} required />
                        </div>
                        <div>
                            <Label htmlFor="stage" className="mb-3">Stage</Label>
                            <Select name="stage" value={form.stage} onValueChange={(value) => handleSelectChange('stage', value)}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select a stage" />
                                </SelectTrigger>
                                <SelectContent>
                                    {OPPORTUNITY_STAGES.map(stage => (
                                        <SelectItem key={stage} value={stage} className="capitalize">{stage.replace('-', ' ')}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="lead_id" className="mb-3">Lead</Label>
                                <Select
                                    name="lead_id"
                                    value={form.lead_id}
                                    onValueChange={(value) => handleSelectChange('lead_id', value)}
                                    key={`lead-select-${form.lead_id}`}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select a lead" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="null">-- None --</SelectItem>
                                        {leads.map(lead => (
                                            <SelectItem key={lead.id} value={lead.id}>{lead.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="contact_id" className="mb-3">Contact</Label>
                                <Select
                                    name="contact_id"
                                    value={form.contact_id}
                                    onValueChange={(value) => handleSelectChange('contact_id', value)}
                                    key={`contact-select-${form.lead_id}`}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select a contact" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="null">-- None --</SelectItem>
                                        {contacts.map(contact => (
                                            <SelectItem key={contact.id} value={contact.id}>{contact?.first_name + " " + contact?.last_name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="expected_close_date" className="mb-3">Expected Close Date</Label>
                            <Input id="expected_close_date" name="expected_close_date" type="date" value={form.expected_close_date} onChange={handleChange} required />
                        </div>
                        <div>
                            <Label htmlFor="notes" className="mb-3">Notes</Label>
                            <Textarea id="notes" name="notes" value={form.notes} onChange={handleChange} />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={showAdd ? handleAddOpportunity : handleEditOpportunity} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            {showAdd ? 'Create Opportunity' : 'Update Opportunity'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}