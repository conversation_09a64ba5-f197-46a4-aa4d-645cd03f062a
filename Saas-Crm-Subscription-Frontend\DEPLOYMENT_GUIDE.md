# SaaS CRM Frontend Deployment Guide

This guide will help you deploy your React SaaS CRM application to an Ubuntu server using Docker and configure it with your Cloudflare domain `tclaccord.com`.

## Prerequisites

- Ubuntu server (18.04 or later)
- Root or sudo access to the server
- Domain `tclaccord.com` managed by Cloudflare
- Basic knowledge of Linux commands

## Quick Start

1. **Upload your project to the server**
   ```bash
   # On your local machine, upload the project
   scp -r . user@your-server-ip:/home/<USER>/saas-crm-frontend
   ```

2. **SSH into your server**
   ```bash
   ssh user@your-server-ip
   ```

3. **Run the deployment script**
   ```bash
   cd saas-crm-frontend
   chmod +x deploy.sh
   ./deploy.sh
   ```

4. **Configure Cloudflare DNS** (see `CLOUDFLARE_SETUP.md`)

## Manual Deployment Steps

If you prefer to deploy manually or need to troubleshoot:

### Step 1: Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### Step 2: Install Docker
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
# Log out and log back in
```

### Step 3: Install Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Step 4: Install Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
```

### Step 5: Install Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### Step 6: Deploy Application
```bash
# Create application directory
sudo mkdir -p /opt/saas-crm-frontend
sudo chown $USER:$USER /opt/saas-crm-frontend

# Copy project files
cp -r . /opt/saas-crm-frontend/
cd /opt/saas-crm-frontend

# Build and start
docker-compose up -d --build
```

### Step 7: Configure Nginx
```bash
# Create nginx configuration
sudo tee /etc/nginx/sites-available/tclaccord.com > /dev/null <<EOF
server {
    listen 80;
    server_name tclaccord.com www.tclaccord.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tclaccord.com www.tclaccord.com;

    ssl_certificate /etc/letsencrypt/live/tclaccord.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/tclaccord.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/tclaccord.com /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx
```

### Step 8: Get SSL Certificate
```bash
sudo certbot --nginx -d tclaccord.com -d www.tclaccord.com --non-interactive --agree-tos --email <EMAIL>
```

## File Structure

After deployment, your server will have this structure:

```
/opt/saas-crm-frontend/
├── Dockerfile
├── docker-compose.yml
├── nginx.conf
├── deploy.sh
├── logs/
│   └── nginx/
└── ssl/
```

## Useful Commands

### Application Management
```bash
# View logs
docker-compose logs -f

# Restart application
docker-compose restart

# Stop application
docker-compose down

# Update application
git pull
docker-compose up -d --build

# Check status
docker-compose ps
```

### System Service
```bash
# Enable auto-start
sudo systemctl enable saas-crm-frontend

# Start service
sudo systemctl start saas-crm-frontend

# Check status
sudo systemctl status saas-crm-frontend
```

### SSL Certificate Management
```bash
# Renew certificates
sudo certbot renew

# Check certificate status
sudo certbot certificates

# Test renewal
sudo certbot renew --dry-run
```

### Logs and Monitoring
```bash
# Application logs
docker-compose logs -f frontend

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -u saas-crm-frontend -f
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   # Check Docker logs
   docker-compose logs
   
   # Check if ports are in use
   sudo netstat -tlnp | grep :4000
   sudo netstat -tlnp | grep :4001
   ```

2. **SSL certificate issues**
   ```bash
   # Check certificate status
   sudo certbot certificates
   
   # Test SSL
   openssl s_client -connect tclaccord.com:443
   ```

3. **Domain not resolving**
   ```bash
   # Check DNS
   nslookup tclaccord.com
   dig tclaccord.com
   
   # Check if server is accessible
   curl -I http://your-server-ip:4000
   ```

4. **Nginx configuration errors**
   ```bash
   # Test configuration
   sudo nginx -t
   
   # Check nginx status
   sudo systemctl status nginx
   ```

### Performance Optimization

1. **Enable Gzip compression** (already configured in nginx.conf)
2. **Set up log rotation** (handled by deploy.sh)
3. **Monitor resource usage**
   ```bash
   # Check Docker resource usage
   docker stats
   
   # Check system resources
   htop
   df -h
   ```

## Security Considerations

1. **Firewall Configuration**
   ```bash
   # Allow only necessary ports
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

2. **Regular Updates**
   ```bash
   # Update system packages
   sudo apt update && sudo apt upgrade -y
   
   # Update Docker images
   docker-compose pull
   docker-compose up -d
   ```

3. **Backup Strategy**
   ```bash
   # Backup application
   tar -czf saas-crm-backup-$(date +%Y%m%d).tar.gz /opt/saas-crm-frontend
   
   # Backup SSL certificates
   sudo tar -czf ssl-backup-$(date +%Y%m%d).tar.gz /etc/letsencrypt
   ```

## Monitoring and Maintenance

### Health Checks
```bash
   # Application health
   curl -f http://localhost:4000/health

# SSL certificate expiry
sudo certbot certificates

# Disk space
df -h

# Memory usage
free -h
```

### Automated Tasks
The deployment script sets up:
- Automatic SSL certificate renewal
- Log rotation
- Systemd service for auto-start
- Health checks

## Support

If you encounter issues:

1. Check the logs first
2. Verify all prerequisites are met
3. Ensure DNS is properly configured
4. Check firewall and network settings
5. Review the troubleshooting section above

Your application should be accessible at `https://tclaccord.com` once deployment is complete and DNS propagation finishes.
