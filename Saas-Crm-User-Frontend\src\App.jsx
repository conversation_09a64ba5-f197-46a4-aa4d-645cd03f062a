import React, { useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import UserPage from './pages/UserPage';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import AddUserPage from './pages/AddUserPage';
import EditUserPage from './pages/EditUserPage';
import AdminLayout from '@saas-crm/shared/AdminLayout';
import { checkAuthAndRedirect } from '@saas-crm/shared/tokenHandler';
import RolePage from './pages/RolePage';
import PermissionPage from './pages/PermissionPage';

function App() {
    useEffect(() => {
        checkAuthAndRedirect();
    }, []);
    return (
        <div className="App">
            <BrowserRouter basename='users'>
                <AdminLayout>
                    <Routes>
                        <Route path="/" element={<UserPage />} />
                        <Route path="/add" element={<AddUserPage />} />
                        <Route path="/edit/:id" element={<EditUserPage />} />
                        <Route path="/role" element={<RolePage />} />
                        <Route path="/permission" element={<PermissionPage />} />
                    </Routes>
                </AdminLayout>
            </BrowserRouter>
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
