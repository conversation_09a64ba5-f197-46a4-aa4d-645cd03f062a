#!/bin/bash

# Build script for Dock<PERSON> with private repository support
set -e

echo "Starting Docker build process..."

# Check if we're in a CI environment
if [ "$CI" = "true" ] || [ "$GITHUB_ACTIONS" = "true" ]; then
    echo "Running in CI environment"
    
    # For CI environments, we'll use build args to pass authentication
    docker build \
        --build-arg NPM_TOKEN=${NPM_TOKEN} \
        --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
        -t saas-crm-frontend:latest \
        .
else
    echo "Running in local environment"
    
    # For local builds, use the standard docker build
    docker build -t saas-crm-frontend:latest .
fi

echo "Docker build completed successfully!"
