import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Ticket,
    Users,
    Briefcase,
    DollarSign,
    UserPlus,
    Headset,
} from 'lucide-react';

const DashboardPage = () => {
    return (
        <div className="min-h-screen bg-gray-100  flex items-center justify-center font-inter">
            <div className="w-full max-w-6xl mx-auto">

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 mb-8">
                    {/* Tickets Widget */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">Open Tickets</CardTitle>
                            <Ticket size={20} className="text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-red-700">125</div>
                            <p className="text-xs text-gray-500 mt-1">New: <span className="font-semibold">15</span>, High Priority: <span className="font-semibold">8</span></p>
                        </CardContent>
                    </Card>

                    {/* Leads Widget */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">New Leads Today</CardTitle>
                            <Briefcase size={20} className="text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-green-700">32</div>
                            <p className="text-xs text-gray-500 mt-1">Qualified: <span className="font-semibold">12</span>, Follow-up: <span className="font-semibold">20</span></p>
                        </CardContent>
                    </Card>

                    {/* Active Users Widget */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">Active Users</CardTitle>
                            <Users size={20} className="text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-blue-700">589</div>
                            <p className="text-xs text-gray-500 mt-1">New Sign-ups: <span className="font-semibold">5</span>, Admins: <span className="font-semibold">10</span></p>
                        </CardContent>
                    </Card>

                    {/* New Card: Customers This Month */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">Customers This Month</CardTitle>
                            <UserPlus size={20} className="text-purple-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-purple-700">45</div>
                            <p className="text-xs text-gray-500 mt-1">Growth: <span className="font-semibold">+12%</span> from last month</p>
                        </CardContent>
                    </Card>

                    {/* New Card: Total Revenue */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">Total Revenue</CardTitle>
                            <DollarSign size={20} className="text-emerald-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-emerald-700">$125,450</div>
                            <p className="text-xs text-gray-500 mt-1">YTD: <span className="font-semibold">$890,120</span></p>
                        </CardContent>
                    </Card>

                    {/* New Card: Support Agents Online */}
                    <Card className="rounded-xl shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-medium text-gray-700">Agents Online</CardTitle>
                            <Headset size={20} className="text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-4xl font-bold text-orange-700">18</div>
                            <p className="text-xs text-gray-500 mt-1">Available: <span className="font-semibold">15</span>, Busy: <span className="font-semibold">3</span></p>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity Section (now also a Shadcn Card) */}
                <Card className="rounded-xl shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl sm:text-2xl font-semibold text-gray-800">Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ul className="list-disc list-inside space-y-2 text-gray-700">
                            <li>User <span className="font-medium">John Doe</span> created a new ticket <span className="font-medium">#1234</span>.</li>
                            <li>Lead <span className="font-medium">Jane Smith</span> updated status to <span className="font-medium">'Contacted'</span>.</li>
                            <li>Ticket <span className="font-medium">#1230</span> resolved by agent <span className="font-medium">Mark</span>.</li>
                            <li>New user account registered: <span className="font-medium">Alice</span>.</li>
                            <li>Ticket <span className="font-medium">#1235</span> assigned to agent <span className="font-medium">Sarah</span>.</li>
                            <li>Lead <span className="font-medium">Bob Johnson</span> requested a demo.</li>
                        </ul>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default DashboardPage;
