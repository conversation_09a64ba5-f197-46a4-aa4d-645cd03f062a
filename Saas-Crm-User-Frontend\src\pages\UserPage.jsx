import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, Loader2, UserRoundPlus } from 'lucide-react';
import apiClient, { apiClientRole } from '@/lib/apiClient';
import toast, { Toaster } from 'react-hot-toast';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import axios from 'axios';

export default function UserPage() {
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [tenants, setTenants] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);
    const [isAssigningRole, setIsAssigningRole] = useState(false);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [showAssignRole, setShowAssignRole] = useState(false);

    const [currentUser, setCurrentUser] = useState(null);
    const [currentUserForRole, setCurrentUserForRole] = useState(null);

    const storedTenantId = localStorage.getItem('tenantId');
    const tenantId = storedTenantId === "null" ? null : storedTenantId;

    const [form, setForm] = useState({
        name: '',
        email: '',
        password: '',
        role: '',
        status: 'active',
        user_type: tenantId ? 'tenant' : 'global',
        tenant_id: ''
    });

    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [selectedRoleIds, setSelectedRoleIds] = useState([]);

    useEffect(() => {
        fetchUsers();
        fetchRoles();
        if (!tenantId) fetchTenants();
    }, []);

    const fetchTenants = async () => {
        setIsLoading(true);
        try {
            const res = await axios.get('https://tenant.tclaccord.com/api/tenants');
            setTenants(res.data?.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchUsers = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/users');
            setUsers(res.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchRoles = async () => {
        try {
            const res = await apiClientRole.get('/roles');
            setRoles(res.data);
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        }
    };

    const fetchUserRole = (userId) => apiClientRole.get(`/roles/user/${userId}`)

    const handleChange = (e) => {
        const { name, value } = e.target;
        setForm({ ...form, [name]: value });
    };

    const handleAddUser = async () => {
        setFormLoading(true);
        try {
            const payload = { ...form };

            if (tenantId) {
                delete payload.tenant_id;
                payload.user_type = 'tenant';
            } else {
                payload.user_type = 'global';
            }

            await apiClient.post('/users', payload);
            toast.success('User added successfully!');
            setShowAdd(false);
            setForm({
                name: '',
                email: '',
                password: '',
                role: '',
                status: 'active',
                user_type: tenantId ? 'tenant' : 'global',
                tenant_id: ''
            });
            fetchUsers();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleEditUser = async () => {
        setFormLoading(true);
        const payload = isEditingPassword
            ? { name: form.name, email: form.email, password: form.password, role: form.role, status: form.status }
            : { name: form.name, email: form.email, role: form.role, status: form.status };

        try {
            await apiClient.patch(`/users/${currentUser.id}`, payload);
            toast.success('User updated successfully!');
            setShowEdit(false);
            setCurrentUser(null);
            setForm({
                name: '',
                email: '',
                password: '',
                role: '',
                status: 'active',
                user_type: tenantId ? 'tenant' : 'global',
                tenant_id: ''
            });
            setIsEditingPassword(false);
            fetchUsers();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteUser = async (id) => {
        setIsLoading(true);
        try {
            await apiClient.delete(`/users/${id}`);
            toast.success('User deleted successfully!');
            fetchUsers();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleAssignRoleToUser = async () => {
        if (selectedRoleIds.length === 0) {
            toast.error("Please select at least one role to assign.");
            return;
        }

        setIsAssigningRole(true);
        try {
            const payload = {
                userId: currentUserForRole.id,
                roleIds: selectedRoleIds
            };
            await apiClientRole.post('/roles/assign-to-user', payload);
            toast.success("Role assigned successfully!");
            setShowAssignRole(false);
            fetchUsers();
        } catch (err) {
            toast.error(err.response?.data?.message || err.message);
        } finally {
            setIsAssigningRole(false);
        }
    };

    const handleRoleCheckboxChange = (roleId, isChecked) => {
        setSelectedRoleIds(prev =>
            isChecked
                ? [...prev, roleId]
                : prev.filter(id => id !== roleId)
        );
    };

    const openAddModal = () => {
        setForm({
            name: '',
            email: '',
            password: '',
            role: '',
            status: 'active',
            user_type: tenantId ? 'tenant' : 'global',
            tenant_id: ''
        });
        setShowAdd(true);
    };

    const openEditModal = (user) => {
        setCurrentUser(user);
        setForm({
            name: user.name,
            email: user.email,
            password: '',
            role: user.role,
            status: user.status,
            user_type: user.user_type,
            tenant_id: user.tenant_id || ''
        });
        setIsEditingPassword(false);
        setShowEdit(true);
    };

    const openAssignRoleModal = (user) => {
        fetchUserRole(user.id).then((res) => {
            const roles = res.data?.roles;
            setSelectedRoleIds(roles.map(r => r.id));
        });
        setCurrentUserForRole(user);
        setShowAssignRole(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <Toaster position="bottom-center" />
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>User Management</CardTitle>
                <Button onClick={openAddModal}>Add User</Button>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="6" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" /> Loading...
                                </TableCell>
                            </TableRow>
                        ) : users.length > 0 ? (
                            users.map((user, index) => (
                                <TableRow key={user.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{user.name}</TableCell>
                                    <TableCell>{user.email}</TableCell>
                                    <TableCell>{user.role}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {user.status}
                                        </span>
                                    </TableCell>
                                    <TableCell className="text-right space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openAssignRoleModal(user)}
                                            title="Assign Role"
                                        >
                                            <UserRoundPlus className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditModal(user)}
                                            title="Edit User"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button title="Delete User" variant="destructive" size="sm">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="6" className="text-center">No users found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Add User Dialog */}
            <Dialog open={showAdd} onOpenChange={setShowAdd}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add User</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className="mb-3">Name</Label>
                            <Input name="name" value={form.name} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3">Email</Label>
                            <Input name="email" value={form.email} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3">Password</Label>
                            <Input name="password" type="password" value={form.password} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3">Role</Label>
                            <Select name="role" value={form.role} onValueChange={(value) => setForm({ ...form, role: value })}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                    {roles.map(role => (
                                        <SelectItem key={role.id} value={role.name}>{role.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Tenant select only if tenantId is null */}
                        {!tenantId && (
                            <div>
                                <Label className="mb-3">Tenant</Label>
                                <Select
                                    name="tenant_id"
                                    value={form.tenant_id}
                                    onValueChange={(value) => setForm({ ...form, tenant_id: value })}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select a tenant" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {tenants.map((tenant) => (
                                            <SelectItem key={tenant.id} value={tenant.id.toString()}>
                                                {tenant.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        )}

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="status"
                                checked={form.status === 'active'}
                                onCheckedChange={(checked) => setForm({ ...form, status: checked ? 'active' : 'inactive' })}
                            />
                            <Label htmlFor="status">{form.status === 'active' ? 'Active' : 'Inactive'}</Label>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleAddUser} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Add
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit User Dialog */}
            <Dialog open={showEdit} onOpenChange={setShowEdit}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className="mb-3">Name</Label>
                            <Input name="name" value={form.name} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3">Email</Label>
                            <Input name="email" value={form.email} onChange={handleChange} />
                        </div>
                        <div>
                            <Label className="mb-3">Role</Label>
                            <Select name="role" value={form.role} onValueChange={(value) => setForm({ ...form, role: value })}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                    {roles.map(role => (
                                        <SelectItem key={role.id} value={role.name}>{role.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="status"
                                checked={form.status === 'active'}
                                onCheckedChange={(checked) => setForm({ ...form, status: checked ? 'active' : 'inactive' })}
                            />
                            <Label htmlFor="status">{form.status === 'active' ? 'Active' : 'Inactive'}</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="edit-password"
                                checked={isEditingPassword}
                                onCheckedChange={(checked) => setIsEditingPassword(checked)}
                            />
                            <Label htmlFor="edit-password">Update Password</Label>
                        </div>
                        {isEditingPassword && (
                            <div>
                                <Label className="mb-3">New Password</Label>
                                <Input name="password" type="password" value={form.password} onChange={handleChange} />
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button onClick={handleEditUser} disabled={formLoading}>
                            {formLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Update
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Assign Role Dialog */}
            <Dialog open={showAssignRole} onOpenChange={setShowAssignRole}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle className="text-center">Assign Role to {currentUserForRole?.name}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-2">
                        <Label className="mb-3">Roles</Label>
                    </div>
                    <div className="grid grid-cols-4 gap-3">
                        {roles.map((role) => (
                            <div key={role.id} className="flex items-center space-x-2">
                                <Checkbox
                                    id={`role-${role.id}`}
                                    checked={selectedRoleIds.includes(role.id)}
                                    onCheckedChange={(checked) =>
                                        handleRoleCheckboxChange(role.id, checked)
                                    }
                                />
                                <Label htmlFor={`role-${role.id}`}>{role.name}</Label>
                            </div>
                        ))}
                    </div>

                    <DialogFooter>
                        <Button onClick={handleAssignRoleToUser} disabled={isAssigningRole}>
                            {isAssigningRole ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                            Assign
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
