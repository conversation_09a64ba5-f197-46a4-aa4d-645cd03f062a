# Docker Build Fix for Private Repository Dependencies

## Problem
The Docker build was failing with multiple errors:

### Error 1: Git not found
```
npm error code ENOENT
npm error syscall spawn git
npm error path git
npm error errno -2
npm error enoent An unknown git error occurred
```

### Error 2: SSH authentication failure
```
<NAME_EMAIL>: Permission denied (publickey).
npm error fatal: Could not read from remote repository.
```

### Error 3: Vite not found
```
sh: vite: not found
```

### Error 4: npm prune failing
```
npm error remote: Invalid username or token. Password authentication is not supported for Git operations.
npm error fatal: Authentication failed for 'https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git/'
```

These were caused by:
1. Git not being installed in the Alpine Linux container
2. Missing SSH client for private repository access
3. No authentication setup for private repositories
4. npm install failing, causing Vite to not be available
5. npm prune trying to access private repository again after successful build

## Solution

### 1. Updated Dockerfile
The Dockerfile has been updated to:
- Install Git and SSH client in the Alpine container
- Configure Git to use HTTPS instead of SSH for better CI/CD compatibility
- Add support for NPM and GitHub authentication tokens
- Include comprehensive Git URL rewriting for all SSH formats
- Add robust fallback mechanism using package.docker.json if private dependency fails
- Include Vite installation verification and debugging information
- Use npx for build commands to ensure proper binary resolution
- Remove npm prune step to avoid private repository access issues in multi-stage build

### 2. Environment Variables
For private repositories, set these environment variables:

```bash
# For private npm packages
export NPM_TOKEN=your_npm_token_here

# For private GitHub repositories
export GITHUB_TOKEN=your_github_token_here
```

### 3. Build Commands

#### Local Build
```bash
docker build -t saas-crm-frontend:latest .
```

#### Build with Authentication (for private repos)
```bash
docker build \
  --build-arg NPM_TOKEN=${NPM_TOKEN} \
  --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
  -t saas-crm-frontend:latest .
```

#### Using Docker Compose
```bash
# Set environment variables first
export NPM_TOKEN=your_token
export GITHUB_TOKEN=your_token

# Then build
docker-compose build
```

### 4. CI/CD Setup
For CI/CD environments (like GitHub Actions, GitLab CI, etc.):

1. Set the environment variables as secrets in your CI/CD platform
2. The build will automatically use the tokens when available
3. The Dockerfile includes fallback behavior when tokens are not provided

### 5. Troubleshooting

If you still encounter issues:

1. **Check if the private dependency is actually needed**: The current `package.json` doesn't show any private dependencies, so this might be a cached issue.

2. **Clear Docker cache**:
   ```bash
   docker system prune -a
   ```

3. **Check for package-lock.json**: If you have a `package-lock.json` file with private dependencies, you may need to regenerate it:
   ```bash
   rm package-lock.json
   npm install
   ```

4. **Verify repository access**: Ensure your tokens have the correct permissions for the private repositories.

### 3. Fallback Package Configuration
- Created `package.docker.json` without the private Git dependency
- Docker build will automatically fall back to this if private dependency fails
- Ensures the build completes successfully even without private repository access

## Files Modified
- `Dockerfile`: Added comprehensive Git, SSH, authentication support, and robust fallback mechanism
- `docker-compose.yml`: Added build arguments for tokens
- `build.sh`: Created build script with CI/CD support
- `docker-build.sh`: Enhanced build script with proper error handling
- `package.docker.json`: Fallback package configuration without private dependencies
- `.dockerignore`: Updated to exclude documentation files
