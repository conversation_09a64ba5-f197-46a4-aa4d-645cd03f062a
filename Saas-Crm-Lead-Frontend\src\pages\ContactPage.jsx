import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, PlusCircle } from 'lucide-react';
import apiClient from '@/lib/apiClient';

export default function ContactPage() {
    const [leads, setLeads] = useState([]);
    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [currentLead, setCurrentLead] = useState(null);
    const [form, setForm] = useState({
        first_name: '',
        last_name: '',
        email: '',
        phone: ''
    });
    const [leadToDeleteId, setLeadToDeleteId] = useState(null);
    const [validationError, setValidationError] = useState('');

    // fetch all contacts
    const fetchContacts = async () => {
        try {
            const { data } = await apiClient.get('/contacts');
            setLeads(data);
        } catch (err) {
            console.error('Fetch contacts error:', err);
        }
    };

    useEffect(() => {
        fetchContacts();
    }, []);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm((prev) => ({ ...prev, [id]: value }));
        setValidationError('');
    };

    // Create contact
    const handleAddLead = async () => {
        if (!form.first_name || !form.last_name || !form.email || !form.phone) {
            setValidationError('All fields are required.');
            return;
        }
        const payload = {
            ...form,
            // created_by: "7d00ad75-61c6-4f95-b574-dcff9ca01586"
        };

        try {
            await apiClient.post('/contacts', payload);
            await fetchContacts();
            setShowAdd(false);
            setForm({ first_name: '', last_name: '', email: '', phone: '' });
        } catch (err) {
            console.error('Add contact error:', err);
        }
    };

    // Update contact
    const handleUpdateLead = async () => {
        if (!form.first_name || !form.last_name || !form.email || !form.phone) {
            setValidationError('All fields are required.');
            return;
        }
        const payload = {
            ...form,
            // created_by: currentLead?.created_by || localStorage.getItem('token')
        };

        try {
            await apiClient.patch(`/contacts/${currentLead.id}`, payload);
            await fetchContacts();
            setShowEdit(false);
            setCurrentLead(null);
        } catch (err) {
            console.error('Update contact error:', err);
        }
    };

    // Delete contact
    const handleDeleteLeadConfirmed = async () => {
        try {
            await apiClient.delete(`/contacts/${leadToDeleteId}`);
            await fetchContacts();
            setLeadToDeleteId(null);
        } catch (err) {
            console.error('Delete contact error:', err);
        }
    };

    const openAddModal = () => {
        setForm({ first_name: '', last_name: '', email: '', phone: '' });
        setValidationError('');
        setShowAdd(true);
    };

    const openEditModal = (lead) => {
        setCurrentLead(lead);
        setForm({
            first_name: lead.first_name,
            last_name: lead.last_name,
            email: lead.email,
            phone: lead.phone
        });
        setValidationError('');
        setShowEdit(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Contacts Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button onClick={openAddModal}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Add Contact
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add New Contact</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div>
                                <Label className={"mb-3"} htmlFor="first_name">First Name</Label>
                                <Input id="first_name" value={form.first_name} onChange={handleInputChange} />
                            </div>
                            <div>
                                <Label className={"mb-3"} htmlFor="last_name">Last Name</Label>
                                <Input id="last_name" value={form.last_name} onChange={handleInputChange} />
                            </div>
                            <div>
                                <Label className={"mb-3"} htmlFor="email">Email</Label>
                                <Input id="email" type="email" value={form.email} onChange={handleInputChange} />
                            </div>
                            <div>
                                <Label className={"mb-3"} htmlFor="phone">Phone</Label>
                                <Input id="phone" type="text" value={form.phone} onChange={handleInputChange} />
                            </div>
                            {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                        </div>
                        <DialogFooter>
                            <Button onClick={handleAddLead}>Add Contact</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                {leads.length === 0 ? (
                    <div className="text-center text-gray-500 py-10">No Contacts found. Add a new one!</div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>#</TableHead>
                                <TableHead>First Name</TableHead>
                                <TableHead>Last Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Phone</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {leads.map((lead, index) => (
                                <TableRow key={lead.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{lead.first_name}</TableCell>
                                    <TableCell>{lead.last_name}</TableCell>
                                    <TableCell>{lead.email}</TableCell>
                                    <TableCell>{lead.phone}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        {/* Edit */}
                                        <Dialog open={showEdit && currentLead?.id === lead.id} onOpenChange={setShowEdit}>
                                            <DialogTrigger asChild>
                                                <Button variant="outline" size="sm" onClick={() => openEditModal(lead)}>
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            </DialogTrigger>
                                            {currentLead && (
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Edit Contact</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="space-y-4 py-4">
                                                        <div>
                                                            <Label className={"mb-3"} htmlFor="first_name">First Name</Label>
                                                            <Input id="first_name" value={form.first_name} onChange={handleInputChange} />
                                                        </div>
                                                        <div>
                                                            <Label className={"mb-3"} htmlFor="last_name">Last Name</Label>
                                                            <Input id="last_name" value={form.last_name} onChange={handleInputChange} />
                                                        </div>
                                                        <div>
                                                            <Label className={"mb-3"} htmlFor="email">Email</Label>
                                                            <Input id="email" type="email" value={form.email} onChange={handleInputChange} />
                                                        </div>
                                                        <div>
                                                            <Label className={"mb-3"} htmlFor="phone">Phone</Label>
                                                            <Input id="phone" type="text" value={form.phone} onChange={handleInputChange} />
                                                        </div>
                                                        {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                                                    </div>
                                                    <DialogFooter>
                                                        <Button onClick={handleUpdateLead}>Save Changes</Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            )}
                                        </Dialog>

                                        {/* Delete */}
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="destructive" size="sm" onClick={() => setLeadToDeleteId(lead.id)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This will permanently delete the contact.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handleDeleteLeadConfirmed}>Delete</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
    );
}
