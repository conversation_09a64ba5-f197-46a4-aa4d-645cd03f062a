
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, PlusCircle, Loader2, UserRoundPlus } from 'lucide-react';
import { apiClientRole as apiClient } from '@/lib/apiClient';
import toast, { Toaster } from 'react-hot-toast';

export default function RolePage() {
    const [roles, setRoles] = useState([]);
    const [permissions, setPermissions] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);
    const [isAssignLoading, setIsAssignLoading] = useState(false);

    const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
    const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
    const [currentRole, setCurrentRole] = useState(null);

    const [form, setForm] = useState({ name: '', description: '' });
    const [selectedPermissions, setSelectedPermissions] = useState(new Set());

    useEffect(() => {
        fetchRoles();
    }, []);

    const fetchRoles = async () => {
        setIsLoading(true);
        try {
            const res = await apiClient.get('/roles');
            setRoles(res.data);
        } catch (err) {
            toast.error("Failed to fetch roles.");
        } finally {
            setIsLoading(false);
        }
    };

    const fetchPermissionsForAssignment = async (roleId) => {
        setIsAssignLoading(true);
        try {
            const [allPermsRes, assignedPermsRes] = await Promise.all([
                apiClient.get('/permissions'),
                apiClient.get(`/roles/${roleId}/permissions`)
            ]);
            setPermissions(allPermsRes.data);
            const assignedIds = new Set(assignedPermsRes.data.map(p => p.id));
            setSelectedPermissions(assignedIds);
        } catch (err) {
            toast.error("Failed to load permissions.");
        } finally {
            setIsAssignLoading(false);
        }
    };

    const handleFormSubmit = async () => {
        setFormLoading(true);
        const isUpdate = !!currentRole;

        try {
            if (isUpdate) {
                await apiClient.patch(`/roles/${currentRole.id}`, form);
                toast.success("Role updated successfully!");
            } else {
                await apiClient.post("/roles", form);
                toast.success("Role created successfully!");
            }
            setIsRoleModalOpen(false);
            setCurrentRole(null);
            setForm({ name: '', description: '' });
            fetchRoles();
        } catch (err) {
            toast.error(`Failed to ${isUpdate ? "update" : "create"} role.`);
        } finally {
            setFormLoading(false);
        }
    };

    const handleDeleteRole = async (id) => {
        setFormLoading(true);
        try {
            await apiClient.delete(`/roles/${id}`);
            toast.success("Role deleted successfully!");
            fetchRoles();
        } catch (err) {
            toast.error("Failed to delete role.");
        } finally {
            setFormLoading(false);
        }
    };

    const handleAssignSubmit = async () => {
        setFormLoading(true);
        try {
            const permissionIds = Array.from(selectedPermissions);
            await apiClient.post(`/roles/${currentRole.id}/permissions`, { permissionIds });
            toast.success("Permissions assigned successfully!");
            setIsAssignModalOpen(false);
        } catch (err) {
            toast.error("Failed to assign permissions.");
        } finally {
            setFormLoading(false);
        }
    };

    const handleAddClick = () => {
        setCurrentRole(null);
        setForm({ name: '', description: '' });
        setIsRoleModalOpen(true);
    };

    const handleEditClick = (role) => {
        setCurrentRole(role);
        setForm({ name: role.name, description: role.description });
        setIsRoleModalOpen(true);
    };

    const handleAssignClick = (role) => {
        setCurrentRole(role);
        fetchPermissionsForAssignment(role.id);
        setIsAssignModalOpen(true);
    };

    const handlePermissionToggle = (permissionId) => {
        setSelectedPermissions(prev => {
            const newSet = new Set(prev);
            if (newSet.has(permissionId)) {
                newSet.delete(permissionId);
            } else {
                newSet.add(permissionId);
            }
            return newSet;
        });
    };

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm(prev => ({ ...prev, [id]: value }));
    };

    return (
        <Card className="p-4 rounded-none">
            <Toaster position="bottom-center" />
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Role Management</CardTitle>
                <Button onClick={handleAddClick}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Role
                </Button>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Role Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">
                                    <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" />
                                    Loading roles...
                                </TableCell>
                            </TableRow>
                        ) : roles.length > 0 ? (
                            roles.map((role, index) => (
                                <TableRow key={role.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{role.name}</TableCell>
                                    <TableCell>{role.description}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        {/* <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleAssignClick(role)}
                                        >
                                            Assign Permissions
                                        </Button> */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleAssignClick(role)}
                                            title="Assign Permissions"
                                        >
                                            <UserRoundPlus className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleEditClick(role)}
                                            title="Edit Role"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button title="Delete Role" variant="destructive" size="sm" disabled={formLoading}>
                                                    {formLoading ? (
                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                    ) : (
                                                        <Trash2 className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone. This will permanently delete the role.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteRole(role.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="4" className="text-center">No roles found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Add/Edit Role Dialog */}
            <Dialog open={isRoleModalOpen} onOpenChange={setIsRoleModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{currentRole ? 'Edit Role' : 'Add Role'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="name" className="mb-3">Name</Label>
                            <Input
                                id="name"
                                value={form.name}
                                onChange={handleInputChange}
                            />
                        </div>
                        <div>
                            <Label htmlFor="description" className="mb-3">Description</Label>
                            <Input
                                id="description"
                                value={form.description}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleFormSubmit} disabled={formLoading}>
                            {formLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                currentRole ? 'Update' : 'Add'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Assign Permissions Dialog */}
            <Dialog open={isAssignModalOpen} onOpenChange={setIsAssignModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Assign Permissions to Role</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label className={"mb-3"} htmlFor="role-name">Role Name</Label>
                            <Input
                                id="role-name"
                                value={currentRole?.name || ''}
                                readOnly
                            />
                        </div>
                        <div>
                            <Label className={"mb-3"}>Permissions</Label>
                            <div className="space-y-2 max-h-60 overflow-y-auto mt-2 p-2 border rounded-md">
                                {isAssignLoading ? (
                                    <div className="text-center">
                                        <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                                        <p>Loading permissions...</p>
                                    </div>
                                ) : (
                                    permissions.map(permission => (
                                        <div key={permission.id} className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id={`permission-${permission.id}`}
                                                checked={selectedPermissions.has(permission.id)}
                                                onChange={() => handlePermissionToggle(permission.id)}
                                                className="cursor-pointer"
                                            />
                                            <Label htmlFor={`permission-${permission.id}`}>{permission.name}</Label>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={handleAssignSubmit} disabled={formLoading || isAssignLoading}>
                            {formLoading ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                'Assign'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}