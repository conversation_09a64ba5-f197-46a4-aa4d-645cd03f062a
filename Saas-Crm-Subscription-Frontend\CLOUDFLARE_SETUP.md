# Cloudflare DNS Configuration for tclaccord.com

This guide will help you configure your Cloudflare DNS settings to point your domain `tclaccord.com` to your Ubuntu server.

## Prerequisites

- Your Ubuntu server is running and accessible
- You have the public IP address of your server
- You have access to your Cloudflare account

## Step 1: Get Your Server's Public IP Address

On your Ubuntu server, run:
```bash
curl -4 ifconfig.me
```

This will display your server's public IP address. Note this down.

## Step 2: Configure DNS Records in Cloudflare

1. **Log into Cloudflare Dashboard**
   - Go to [https://dash.cloudflare.com](https://dash.cloudflare.com)
   - Select your domain `tclaccord.com`

2. **Navigate to DNS Settings**
   - Click on "DNS" in the left sidebar
   - Click "Records"

3. **Configure DNS Records**

   You need to create/update the following DNS records:

   ### A Record for Root Domain
   ```
   Type: A
   Name: @
   IPv4 address: YOUR_SERVER_IP
   Proxy status: Proxied (orange cloud)
   TTL: Auto
   ```

   ### A Record for WWW Subdomain
   ```
   Type: A
   Name: www
   IPv4 address: YOUR_SERVER_IP
   Proxy status: Proxied (orange cloud)
   TTL: Auto
   ```

   ### Optional: CNAME for API Subdomain (if you have a backend API)
   ```
   Type: CNAME
   Name: api
   Target: tclaccord.com
   Proxy status: Proxied (orange cloud)
   TTL: Auto
   ```

## Step 3: SSL/TLS Configuration

1. **Go to SSL/TLS Settings**
   - Click "SSL/TLS" in the left sidebar
   - Click "Overview"

2. **Set Encryption Mode**
   - Select "Full (strict)" mode
   - This ensures end-to-end encryption between Cloudflare and your server

3. **Enable Always Use HTTPS**
   - Go to "Edge Certificates"
   - Toggle "Always Use HTTPS" to ON

## Step 4: Performance Optimizations

1. **Enable Caching**
   - Go to "Caching" in the left sidebar
   - Set "Caching Level" to "Standard"
   - Set "Browser Cache TTL" to "4 hours"

2. **Enable Auto Minify**
   - In "Speed" section
   - Enable "Auto Minify" for HTML, CSS, and JavaScript

3. **Enable Brotli Compression**
   - In "Speed" section
   - Enable "Brotli" compression

## Step 5: Security Settings

1. **Security Level**
   - Go to "Security" → "Settings"
   - Set "Security Level" to "Medium"

2. **Enable Bot Fight Mode**
   - Go to "Security" → "Bots"
   - Enable "Bot Fight Mode"

3. **Enable DDoS Protection**
   - Go to "Security" → "DDoS"
   - Ensure "DDoS Protection" is enabled

## Step 6: Page Rules (Optional)

Create page rules for better performance:

1. **Go to "Rules" → "Page Rules"**
2. **Create a new rule for static assets:**
   ```
   URL Pattern: tclaccord.com/*.js
   Settings:
   - Cache Level: Cache Everything
   - Edge Cache TTL: 1 month
   - Browser Cache TTL: 1 month
   ```

3. **Create a rule for CSS files:**
   ```
   URL Pattern: tclaccord.com/*.css
   Settings:
   - Cache Level: Cache Everything
   - Edge Cache TTL: 1 month
   - Browser Cache TTL: 1 month
   ```

## Step 7: Verify Configuration

After making these changes:

1. **Wait for DNS Propagation**
   - DNS changes can take up to 24 hours to propagate globally
   - Usually takes 5-15 minutes for most users

2. **Test Your Domain**
   ```bash
   # Test DNS resolution
   nslookup tclaccord.com
   nslookup www.tclaccord.com
   
   # Test HTTP/HTTPS
   curl -I https://tclaccord.com
   curl -I https://www.tclaccord.com
   ```

3. **Check SSL Certificate**
   - Visit `https://tclaccord.com` in your browser
   - Verify the SSL certificate is valid
   - Check that the site loads correctly

## Troubleshooting

### Common Issues:

1. **Domain not resolving**
   - Check if DNS records are correctly configured
   - Verify the server IP address is correct
   - Wait for DNS propagation

2. **SSL certificate issues**
   - Ensure "Full (strict)" SSL mode is enabled
   - Check that your server is properly configured for HTTPS
   - Verify Let's Encrypt certificates are working

3. **Site not loading**
   - Check if your server is running
   - Verify firewall settings allow HTTP/HTTPS traffic
   - Check server logs: `docker-compose logs -f`

### Useful Commands:

```bash
# Check if your server is accessible
curl -I http://YOUR_SERVER_IP

# Check SSL certificate
openssl s_client -connect tclaccord.com:443 -servername tclaccord.com

# Test DNS resolution
dig tclaccord.com
dig www.tclaccord.com
```

## Additional Cloudflare Features

Consider enabling these features for better performance and security:

1. **Cloudflare Workers** - For edge computing
2. **Cloudflare Analytics** - For traffic insights
3. **Cloudflare WAF** - For web application firewall
4. **Cloudflare Access** - For authentication
5. **Cloudflare Stream** - For video delivery (if needed)

## Support

If you encounter any issues:

1. Check Cloudflare's status page: [https://www.cloudflarestatus.com](https://www.cloudflarestatus.com)
2. Review Cloudflare documentation: [https://developers.cloudflare.com](https://developers.cloudflare.com)
3. Check your server logs and Docker container status

Your domain should be fully functional at `https://tclaccord.com` once DNS propagation is complete!
