# ---------- Stage 1: Build ----------
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first (better caching)
COPY package*.json ./

# Install dependencies (include dev deps for build)
RUN npm ci

# Copy source code
COPY . .

# Accept build-time variables (must start with VITE_ for Vite to embed)
ARG VITE_API_BASE_URL
ARG VITE_LOGIN_URL
ARG VITE_API_BASE_URL_AUTH

# Export them as ENV so Vite can access during build
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_LOGIN_URL=$VITE_LOGIN_URL
ENV VITE_API_BASE_URL_AUTH=$VITE_API_BASE_URL_AUTH

# Build the Vite project
RUN npm run build


# ---------- Stage 2: Production ----------
FROM nginx:alpine

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
