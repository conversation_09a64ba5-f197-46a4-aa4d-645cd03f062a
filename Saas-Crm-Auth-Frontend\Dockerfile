# ---------- Stage 1: Build ----------
FROM node:20-alpine AS builder

# Install Git and SSH client for private repository access
RUN apk add --no-cache git openssh-client curl

# Set working directory
WORKDIR /app

# Copy package files first (better caching)
COPY package*.json ./
COPY package.docker.json ./
COPY .npmrc ./

# Configure npm for private repositories (if tokens are provided)
ARG NPM_TOKEN
ARG GITHUB_TOKEN

# Set up npm authentication for private packages
RUN if [ -n "$NPM_TOKEN" ]; then \
    echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > /root/.npmrc; \
    fi

# Configure npm to use Git for Git dependencies
RUN npm config set git-tag-version false && \
    npm config set fund false && \
    npm config set audit false

# Configure Git to use HTTPS instead of SSH for private repos (safer for CI/CD)
RUN git config --global url."https://github.com/".insteadOf "**************:" && \
    git config --global url."https://github.com/".insteadOf "ssh://**************/"

# Configure Git to use token authentication for private repos if token is provided
RUN if [ -n "$GITHUB_TOKEN" ]; then \
    git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/" && \
    git config --global url."https://$<EMAIL>/".insteadOf "**************:" && \
    git config --global url."https://$<EMAIL>/".insteadOf "ssh://**************/"; \
    fi

# Install dependencies with fallback logic
RUN echo "Starting dependency installation..." && \
    if npm ci --no-audit --no-fund --verbose 2>&1; then \
    echo "Dependencies installed successfully with private repository"; \
    else \
    echo "Private repository failed, trying fallback approach..." && \
    echo "Backing up original package.json..." && \
    cp package.json package.json.backup && \
    echo "Using package.docker.json without private Git dependency..." && \
    cp package.docker.json package.json && \
    echo "Installing dependencies without private Git dependency..." && \
    npm cache clean --force && \
    npm ci --no-audit --no-fund --verbose && \
    echo "Fallback installation completed successfully"; \
    fi

# Copy source code
COPY . .

# Accept build-time variables (must start with VITE_ for Vite to embed)
ARG VITE_API_BASE_URL
ARG VITE_LOGIN_URL
ARG VITE_API_BASE_URL_AUTH

# Export them as ENV so Vite can access during build
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_LOGIN_URL=$VITE_LOGIN_URL
ENV VITE_API_BASE_URL_AUTH=$VITE_API_BASE_URL_AUTH

# Build the Vite project
RUN npm run build

# Verify build output
RUN ls -la /app/dist/

# ---------- Stage 2: Production ----------
FROM nginx:alpine

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Verify files are copied
RUN ls -la /usr/share/nginx/html/

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
